import 'package:flutter/material.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';
import '../../domain/entities/flashcard.dart';
import '../../domain/entities/deck.dart';
import '../widgets/flashcard_widget.dart';

class PracticeSessionView extends StatefulWidget {
  final Deck deck;
  final List<Flashcard> flashcards;

  const PracticeSessionView({
    super.key,
    required this.deck,
    required this.flashcards,
  });

  @override
  State<PracticeSessionView> createState() => _PracticeSessionViewState();
}

class _PracticeSessionViewState extends State<PracticeSessionView> {
  late List<Flashcard> _sessionCards;
  int _currentIndex = 0;
  int _correctAnswers = 0;
  int _totalAnswered = 0;
  bool _sessionComplete = false;

  @override
  void initState() {
    super.initState();
    _sessionCards = List.from(widget.flashcards)..shuffle();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    if (_sessionComplete) {
      return _buildCompletionScreen(theme, size);
    }

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Container(
        decoration: _buildBackgroundDecoration(theme),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(theme),
              Expanded(
                child: _buildCardSection(theme, size),
              ),
            ],
          ),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundDecoration(ThemeData theme) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          theme.colorScheme.surface,
          theme.colorScheme.surface.withValues(alpha: 0.8),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    final progress = _sessionCards.isEmpty ? 0.0 : (_currentIndex + 1) / _sessionCards.length;
    
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                icon: Icon(
                  PhosphorIcons.arrowLeft(),
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
              Expanded(
                child: Text(
                  widget.deck.name,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              IconButton(
                icon: Icon(
                  PhosphorIcons.x(),
                  color: theme.colorScheme.onSurface,
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Card ${_currentIndex + 1} of ${_sessionCards.length}',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
            color: theme.colorScheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildCardSection(ThemeData theme, Size size) {
    if (_sessionCards.isEmpty) {
      return Center(
        child: Text(
          'No cards to practice',
          style: theme.textTheme.headlineSmall,
        ),
      );
    }

    return FlashCardWidget(
      flashCard: _sessionCards[_currentIndex],
      currentIndex: _currentIndex,
      totalCards: _sessionCards.length,
      onAnswer: _handleAnswer,
      onNext: _nextCard,
      onPrevious: _previousCard,
    );
  }

  Widget _buildCompletionScreen(ThemeData theme, Size size) {
    final accuracy = _totalAnswered == 0 ? 0.0 : (_correctAnswers / _totalAnswered);

    return Scaffold(
      body: Container(
        decoration: _buildBackgroundDecoration(theme),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  PhosphorIcons.trophy(),
                  size: 80,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(height: 24),
                Text(
                  'Session Complete!',
                  style: theme.textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'Accuracy: ${(accuracy * 100).toInt()}%',
                  style: theme.textTheme.headlineSmall,
                ),
                Text(
                  'Correct: $_correctAnswers/${_sessionCards.length}',
                  style: theme.textTheme.titleMedium,
                ),
                const SizedBox(height: 32),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Back to Deck'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleAnswer(bool isCorrect) {
    setState(() {
      _totalAnswered++;
      if (isCorrect) {
        _correctAnswers++;
      }
    });

    // Auto advance after a delay
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) _nextCard();
    });
  }

  void _nextCard() {
    if (_currentIndex < _sessionCards.length - 1) {
      setState(() {
        _currentIndex++;
      });
    } else {
      setState(() {
        _sessionComplete = true;
      });
    }
  }

  void _previousCard() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
      });
    }
  }
}
