#!/bin/bash

# 🚀 TURBO macOS BUILD - Maximum Performance for M3 Max
# Native Apple Silicon compilation with all optimizations

echo "🚀 TURBO macOS BUILD - M3 Max Native Performance"
echo "Hardware: M3 Max (16 cores, 128GB RAM) - Apple Silicon Native"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_turbo() {
    echo -e "${PURPLE}[TURBO]${NC} $1"
}

print_native() {
    echo -e "${CYAN}[NATIVE]${NC} $1"
}

# Set environment variables for maximum macOS performance
export FLUTTER_BUILD_PARALLEL=true
export XCODE_XCCONFIG_FILE="macos/M3MaxOptimizations.xcconfig"

# Xcode build optimizations for M3 Max
export XCODEBUILD_OPTS="-parallelizeTargets -jobs 16"
export SWIFT_COMPILATION_MODE="singlefile"

# macOS specific optimizations
export MACOSX_DEPLOYMENT_TARGET="10.14"
export ARCHS="arm64"

print_turbo "Environment optimized for M3 Max native macOS builds"

# Build type selection
echo ""
echo "🎯 Select macOS build type:"
echo "1) Debug (fastest, ~5-15s)"
echo "2) Release (optimized, ~30-60s)"
echo "3) Profile (performance testing, ~20-40s)"

read -p "Enter your choice (1-3): " build_type

case $build_type in
    1) BUILD_MODE="debug" ;;
    2) BUILD_MODE="release" ;;
    3) BUILD_MODE="profile" ;;
    *) 
        print_status "Invalid choice. Using debug mode for speed."
        BUILD_MODE="debug"
        ;;
esac

print_turbo "Building macOS app in $BUILD_MODE mode with native optimizations..."

# Pre-build optimizations
print_status "Performing pre-build optimizations..."

# Enable macOS desktop
flutter config --enable-macos-desktop

# Clean and prepare with parallel operations
print_status "Cleaning and preparing build environment..."
flutter clean &
clean_pid=$!

flutter pub get &
pub_pid=$!

# Parallel CocoaPods installation
cd macos
print_status "Installing CocoaPods dependencies with optimizations..."
pod install --repo-update &
pod_pid=$!
cd ..

# Wait for operations to complete
wait $clean_pid
print_success "Clean completed"

wait $pub_pid
print_success "Dependencies resolved"

wait $pod_pid
print_success "macOS dependencies resolved"

# Code generation (if needed)
if [ -f "build.yaml" ] || grep -q "build_runner" pubspec.yaml; then
    print_status "Running code generation..."
    dart run build_runner build --delete-conflicting-outputs &
    codegen_pid=$!
    wait $codegen_pid
    print_success "Code generation completed"
fi

# Create optimized build directory
mkdir -p builds/turbo-macos-$BUILD_MODE
build_start_time=$(date +%s)

print_turbo "🚀 Starting NATIVE M3 Max macOS build..."
print_native "Utilizing all 16 cores for maximum performance"

# Performance monitoring
monitor_build() {
    local build_pid=$1
    while kill -0 $build_pid 2>/dev/null; do
        cpu_usage=$(ps -p $build_pid -o %cpu= 2>/dev/null | tr -d ' ')
        if [ ! -z "$cpu_usage" ]; then
            print_native "Build CPU usage: ${cpu_usage}%"
        fi
        sleep 3
    done
}

# Execute optimized macOS build
case $BUILD_MODE in
    "debug")
        print_turbo "Building macOS DEBUG with maximum speed..."
        flutter build macos --debug \
            --dart-define=flutter.inspector.structuredErrors=false \
            --dart-define=M3_MAX_OPTIMIZED=true \
            --dart-define=MACOS_NATIVE=true &
        build_pid=$!
        ;;
    "release")
        print_turbo "Building macOS RELEASE with full optimization..."
        flutter build macos --release \
            --dart-define=flutter.inspector.structuredErrors=false \
            --dart-define=M3_MAX_OPTIMIZED=true \
            --dart-define=MACOS_NATIVE=true &
        build_pid=$!
        ;;
    "profile")
        print_turbo "Building macOS PROFILE for performance testing..."
        flutter build macos --profile \
            --dart-define=flutter.inspector.structuredErrors=false \
            --dart-define=M3_MAX_OPTIMIZED=true \
            --dart-define=MACOS_NATIVE=true &
        build_pid=$!
        ;;
esac

# Monitor build performance
monitor_build $build_pid &
monitor_pid=$!

# Wait for build to complete
wait $build_pid
build_result=$?

# Stop monitoring
kill $monitor_pid 2>/dev/null

build_end_time=$(date +%s)
total_duration=$((build_end_time - build_start_time))

if [ $build_result -eq 0 ]; then
    print_success "🎉 macOS build completed successfully!"
    
    # Copy build artifacts
    cp -r build/macos builds/turbo-macos-$BUILD_MODE/
    
    print_turbo "📊 Build Performance Summary:"
    print_native "Total build time: ${total_duration}s"
    print_native "Hardware: M3 Max (16 cores, 128GB RAM)"
    print_native "Architecture: Apple Silicon (arm64)"
    print_native "Optimization: Maximum native performance"
    
    # Display build size
    if [ -d "builds/turbo-macos-$BUILD_MODE" ]; then
        echo ""
        print_status "📦 Build size:"
        du -sh builds/turbo-macos-$BUILD_MODE/* 2>/dev/null
    fi
    
    # Performance analysis
    echo ""
    if [ $total_duration -lt 15 ]; then
        print_success "🚀 EXCELLENT: macOS build under 15s (M3 Max optimized)"
    elif [ $total_duration -lt 30 ]; then
        print_success "✅ GOOD: macOS build under 30s"
    elif [ $total_duration -lt 60 ]; then
        print_status "⚡ ACCEPTABLE: macOS build under 60s"
    else
        print_status "⚠️  Consider checking system resources or dependencies"
    fi
    
    echo ""
    print_turbo "🎉 TURBO macOS BUILD COMPLETED!"
    print_status "Next steps:"
    echo "  🧪 Test the macOS app: open builds/turbo-macos-$BUILD_MODE/macos/Build/Products/$BUILD_MODE/flash_cards_app.app"
    echo "  🚀 For development: ./scripts/turbo_dev.sh (select macOS)"
    echo "  📊 Monitor performance: ./scripts/performance_monitor.sh"
    
else
    print_error "❌ macOS build failed"
    echo "Check the build logs for errors"
    exit 1
fi

echo ""
print_native "🍎 M3 Max Native macOS Build Optimizations Applied:"
echo "  ⚡ 16-core parallel compilation"
echo "  🧠 128GB RAM utilization"
echo "  🔥 Apple Silicon native architecture"
echo "  🚀 Xcode build system optimizations"
echo "  💾 Intelligent caching strategies"
